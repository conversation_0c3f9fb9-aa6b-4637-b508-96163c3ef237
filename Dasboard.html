<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc; /* Lighter blue-gray background */
        }
        .container {
            background-color: #ffffff;
            border-radius: 0.75rem; /* rounded-xl */
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
        }
        .note-card {
            background-color: #ffffff;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid #e5e7eb;
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .note-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .debit-note-card {
            border-left: 4px solid #4f46e5; /* Indigo 600 */
        }
        .credit-note-card {
            border-left: 4px solid #10b981; /* Emerald 500 */
        }
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body class="min-h-screen flex flex-col items-center p-4 sm:p-6 bg-gray-50">



    <div class="container mx-auto p-6 sm:p-8 max-w-7xl w-full">
        <h1 class="text-3xl sm:text-4xl font-bold text-gray-800 mb-8 text-center">Credit & Debit Notes Dashboard</h1>

        <!-- Original Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="summary-card">
                <h3 class="text-lg font-semibold mb-2">Total Debit Notes</h3>
                <p class="text-sm opacity-80 mb-1">(Money Received)</p>
                <p id="total-debit-amount" class="text-3xl font-bold">$0.00</p>
            </div>
            <div class="summary-card">
                <h3 class="text-lg font-semibold mb-2">Total Credit Notes</h3>
                <p class="text-sm opacity-80 mb-1">(Money Paid)</p>
                <p id="total-credit-amount" class="text-3xl font-bold">$0.00</p>
            </div>
            <div class="summary-card">
                <h3 class="text-lg font-semibold mb-2">Net Amount</h3>
                <p class="text-sm opacity-80 mb-1">(Received - Paid)</p>
                <p id="net-amount" class="text-3xl font-bold">$0.00</p>
            </div>
        </div>

        <!-- Time-based Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <!-- Today -->
            <div class="summary-card">
                <h3 class="text-lg font-semibold mb-3">Today</h3>
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-indigo-200">Debit Notes:</span>
                        <span id="today-debit-count" class="text-xl font-bold">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-emerald-200">Credit Notes:</span>
                        <span id="today-credit-count" class="text-xl font-bold">0</span>
                    </div>
                </div>
            </div>

            <!-- Weekly Created Notes -->
            <div class="summary-card">
                <h3 class="text-lg font-semibold mb-3">Week</h3>
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-indigo-200">Debit Notes:</span>
                        <span id="week-debit-count" class="text-xl font-bold">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-emerald-200">Credit Notes:</span>
                        <span id="week-credit-count" class="text-xl font-bold">0</span>
                    </div>
                </div>
            </div>

            <!-- Monthly Created Notes -->
            <div class="summary-card">
                <h3 class="text-lg font-semibold mb-3">MTD</h3>
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-indigo-200">Debit Notes:</span>
                        <span id="month-debit-count" class="text-xl font-bold">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-emerald-200">Credit Notes:</span>
                        <span id="month-credit-count" class="text-xl font-bold">0</span>
                    </div>
                </div>
            </div>

            <!-- Yearly Created Notes -->
            <div class="summary-card">
                <h3 class="text-lg font-semibold mb-3">Year</h3>
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-indigo-200">Debit Notes:</span>
                        <span id="year-debit-count" class="text-xl font-bold">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-emerald-200">Credit Notes:</span>
                        <span id="year-credit-count" class="text-xl font-bold">0</span>
                    </div>
                </div>
            </div>

            <!-- Total Created Notes -->
            <div class="summary-card">
                <h3 class="text-lg font-semibold mb-3">Total</h3>
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-indigo-200">Debit Notes:</span>
                        <span id="total-debit-count" class="text-xl font-bold">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-emerald-200">Credit Notes:</span>
                        <span id="total-credit-count" class="text-xl font-bold">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Created Notes Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">Created Notes Overview</h2>

            <!-- Debit Notes Grid -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-semibold text-gray-700 flex items-center">
                        <span class="w-3 h-3 bg-indigo-600 rounded-full mr-2"></span>
                        Debit Notes (Money Received)
                    </h3>
                    <span id="debit-notes-count-badge" class="bg-indigo-100 text-indigo-800 text-sm font-medium px-3 py-1 rounded-full">
                        0 Notes
                    </span>
                </div>
                <div id="debit-notes-grid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                    <div id="no-debit-notes" class="col-span-full">
                        <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                            <div class="text-gray-400 mb-2">
                                <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <p class="text-gray-500 text-sm">No debit notes created yet</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Credit Notes Grid -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-semibold text-gray-700 flex items-center">
                        <span class="w-3 h-3 bg-emerald-500 rounded-full mr-2"></span>
                        Credit Notes (Money Paid)
                    </h3>
                    <span id="credit-notes-count-badge" class="bg-emerald-100 text-emerald-800 text-sm font-medium px-3 py-1 rounded-full">
                        0 Notes
                    </span>
                </div>
                <div id="credit-notes-grid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                    <div id="no-credit-notes" class="col-span-full">
                        <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                            <div class="text-gray-400 mb-2">
                                <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <p class="text-gray-500 text-sm">No credit notes created yet</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Party Ledger Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">Party Ledger</h2>
            <div id="party-ledger-container" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <div id="no-party-ledger" class="col-span-full">
                    <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                        <div class="text-gray-400 mb-2">
                            <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <p class="text-gray-500 text-sm">No party ledger entries yet</p>
                        <p class="text-gray-400 text-xs mt-1">Create debit/credit notes to see customer-wise summaries</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-10 space-y-4">
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a href="CreditDebitUI.html" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out transform hover:scale-105">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    Go to Transaction Management
                </a>
                <button id="party-ledger-btn" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition duration-300 ease-in-out transform hover:scale-105">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    View Party Ledger Details
                </button>
            </div>
        </div>
    </div>

    <script>
        /**
         * Formats a number as currency.
         * @param {number} amount - The amount to format.
         * @returns {string} The formatted currency string.
         */
        function formatCurrency(amount) {
            return `$${amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
        }

        /**
         * Loads saved notes from localStorage and displays them on the dashboard
         */
        function loadAndDisplayNotes() {
            const savedData = localStorage.getItem('creditDebitNotes');

            if (!savedData) {
                console.log('No saved notes found in localStorage');
                return;
            }

            try {
                const notesData = JSON.parse(savedData);
                console.log('Loaded notes data:', notesData);

                displayDebitNotes(notesData.debitNotes || {});
                displayCreditNotes(notesData.creditNotes || {});
                updateSummary(notesData.debitNotes || {}, notesData.creditNotes || {}, notesData.timestamp);
                loadAndDisplayPartyLedger();
            } catch (error) {
                console.error('Error parsing saved notes data:', error);
            }
        }

        /**
         * Loads and displays party ledger data
         */
        function loadAndDisplayPartyLedger() {
            const savedPartyLedger = localStorage.getItem('partyLedger');

            if (!savedPartyLedger) {
                console.log('No party ledger data found');
                return;
            }

            try {
                const partyLedgerData = JSON.parse(savedPartyLedger);
                displayPartyLedger(partyLedgerData);
            } catch (error) {
                console.error('Error parsing party ledger data:', error);
            }
        }

        /**
         * Displays debit notes on the dashboard
         * @param {object} debitNotes - Object containing debit notes data
         */
        function displayDebitNotes(debitNotes) {
            const container = document.getElementById('debit-notes-grid');
            const noNotesMessage = document.getElementById('no-debit-notes');
            const countBadge = document.getElementById('debit-notes-count-badge');

            const noteIds = Object.keys(debitNotes);

            if (noteIds.length === 0) {
                noNotesMessage.style.display = 'block';
                countBadge.textContent = '0 Notes';
                return;
            }

            noNotesMessage.style.display = 'none';
            countBadge.textContent = `${noteIds.length} Note${noteIds.length > 1 ? 's' : ''}`;

            // Clear existing content except the no-notes message
            container.innerHTML = '';
            container.appendChild(noNotesMessage);

            noteIds.forEach(noteId => {
                const noteData = debitNotes[noteId];
                const noteCard = createGridNoteCard(noteId, noteData, 'debit');
                container.appendChild(noteCard);
            });
        }

        /**
         * Displays credit notes on the dashboard
         * @param {object} creditNotes - Object containing credit notes data
         */
        function displayCreditNotes(creditNotes) {
            const container = document.getElementById('credit-notes-grid');
            const noNotesMessage = document.getElementById('no-credit-notes');
            const countBadge = document.getElementById('credit-notes-count-badge');

            const noteIds = Object.keys(creditNotes);

            if (noteIds.length === 0) {
                noNotesMessage.style.display = 'block';
                countBadge.textContent = '0 Notes';
                return;
            }

            noNotesMessage.style.display = 'none';
            countBadge.textContent = `${noteIds.length} Note${noteIds.length > 1 ? 's' : ''}`;

            // Clear existing content except the no-notes message
            container.innerHTML = '';
            container.appendChild(noNotesMessage);

            noteIds.forEach(noteId => {
                const noteData = creditNotes[noteId];
                const noteCard = createGridNoteCard(noteId, noteData, 'credit');
                container.appendChild(noteCard);
            });
        }

        /**
         * Creates a compact grid note card element
         * @param {string} noteId - The note ID
         * @param {object} noteData - The note data containing transactions and total
         * @param {string} type - 'debit' or 'credit'
         * @returns {HTMLElement} The created note card element
         */
        function createGridNoteCard(noteId, noteData, type) {
            const card = document.createElement('div');
            card.className = `bg-white rounded-lg shadow-md border-l-4 ${type === 'debit' ? 'border-indigo-500' : 'border-emerald-500'} p-4 hover:shadow-lg transition-shadow duration-200 cursor-pointer`;

            const headerColor = type === 'debit' ? 'text-indigo-600' : 'text-emerald-600';
            const bgColor = type === 'debit' ? 'bg-indigo-50' : 'bg-emerald-50';
            const iconColor = type === 'debit' ? 'text-indigo-500' : 'text-emerald-500';

            card.innerHTML = `
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center">
                        <div class="${bgColor} p-2 rounded-lg mr-3">
                            <svg class="w-4 h-4 ${iconColor}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                ${type === 'debit' ?
                                    '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>' :
                                    '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>'
                                }
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800 text-sm truncate" title="${noteId}">${noteId}</h4>
                            <p class="text-xs text-gray-500">${type.charAt(0).toUpperCase() + type.slice(1)} Note</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="text-xs font-medium px-2 py-1 rounded-full ${type === 'debit' ? 'bg-indigo-100 text-indigo-800' : 'bg-emerald-100 text-emerald-800'}">
                            ${noteData.taxType || 'No Tax'}
                        </span>
                    </div>
                </div>

                <div class="mb-3">
                    <p class="text-xl font-bold ${headerColor}">${formatCurrency(noteData.totalAmount || 0)}</p>
                    <p class="text-xs text-gray-500">${noteData.transactions.length} transaction${noteData.transactions.length !== 1 ? 's' : ''}</p>
                </div>

                <div class="space-y-1">
                    ${noteData.transactions.slice(0, 2).map(transaction => `
                        <div class="text-xs bg-gray-50 p-2 rounded border-l-2 ${type === 'debit' ? 'border-indigo-200' : 'border-emerald-200'}">
                            <div class="font-medium text-gray-700 truncate" title="${transaction.description}">${transaction.description}</div>
                            <div class="flex justify-between items-center mt-1">
                                <span class="text-gray-500">${transaction.id}</span>
                                <span class="font-semibold ${headerColor}">${formatCurrency(transaction.amount)}</span>
                            </div>
                        </div>
                    `).join('')}
                    ${noteData.transactions.length > 2 ? `
                        <div class="text-xs text-center py-2 text-gray-500 bg-gray-50 rounded border-2 border-dashed border-gray-200">
                            +${noteData.transactions.length - 2} more transaction${noteData.transactions.length - 2 !== 1 ? 's' : ''}
                        </div>
                    ` : ''}
                </div>

                <div class="mt-3 pt-3 border-t border-gray-100">
                    <div class="flex items-center justify-between text-xs text-gray-500">
                        <span>Created: Today</span>
                        <span class="flex items-center">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            View Details
                        </span>
                    </div>
                </div>
            `;

            // Add click event to show more details (optional)
            card.addEventListener('click', () => {
                console.log(`Clicked on ${type} note: ${noteId}`);
                // You can add a modal or navigation to detailed view here
            });

            return card;
        }

        /**
         * Creates a note card element (legacy function for compatibility)
         * @param {string} noteId - The note ID
         * @param {object} noteData - The note data containing transactions and total
         * @param {string} type - 'debit' or 'credit'
         * @returns {HTMLElement} The created note card element
         */
        function createNoteCard(noteId, noteData, type) {
            return createGridNoteCard(noteId, noteData, type);
        }

        /**
         * Gets the start of today
         * @returns {Date} Start of today
         */
        function getStartOfToday() {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            return today;
        }

        /**
         * Gets the start of current week (Monday)
         * @returns {Date} Start of current week
         */
        function getStartOfWeek() {
            const today = new Date();
            const day = today.getDay();
            const diff = today.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
            const monday = new Date(today.setDate(diff));
            monday.setHours(0, 0, 0, 0);
            return monday;
        }

        /**
         * Gets the start of current month
         * @returns {Date} Start of current month
         */
        function getStartOfMonth() {
            const today = new Date();
            return new Date(today.getFullYear(), today.getMonth(), 1);
        }

        /**
         * Gets the start of current year
         * @returns {Date} Start of current year
         */
        function getStartOfYear() {
            const today = new Date();
            return new Date(today.getFullYear(), 0, 1);
        }

        /**
         * Checks if a timestamp falls within a date range
         * @param {string} timestamp - ISO timestamp string
         * @param {Date} startDate - Start date for comparison
         * @returns {boolean} True if timestamp is within range
         */
        function isWithinDateRange(timestamp, startDate) {
            const noteDate = new Date(timestamp);
            return noteDate >= startDate;
        }

        /**
         * Displays party ledger data
         * @param {object} partyLedgerData - Party ledger data
         */
        function displayPartyLedger(partyLedgerData) {
            const container = document.getElementById('party-ledger-container');
            const noPartyLedgerMessage = document.getElementById('no-party-ledger');

            const customerIds = Object.keys(partyLedgerData);

            if (customerIds.length === 0) {
                noPartyLedgerMessage.style.display = 'block';
                return;
            }

            noPartyLedgerMessage.style.display = 'none';

            // Clear existing content except the no-party-ledger message
            container.innerHTML = '';
            container.appendChild(noPartyLedgerMessage);

            customerIds.forEach(customerId => {
                const customerData = partyLedgerData[customerId];
                const partyCard = createPartyLedgerCard(customerId, customerData);
                container.appendChild(partyCard);
            });
        }

        /**
         * Creates a party ledger card
         * @param {string} customerId - Customer ID
         * @param {object} customerData - Customer data
         * @returns {HTMLElement} The created party card element
         */
        function createPartyLedgerCard(customerId, customerData) {
            const card = document.createElement('div');
            card.className = 'bg-white rounded-lg shadow-md border-l-4 border-blue-500 p-4 hover:shadow-lg transition-shadow duration-200 cursor-pointer';

            const netAmount = customerData.totalDebit - customerData.totalCredit;
            const netColor = netAmount >= 0 ? 'text-green-600' : 'text-red-600';
            const netLabel = netAmount >= 0 ? 'Net Receivable' : 'Net Payable';

            card.innerHTML = `
                <div class="flex items-start justify-between mb-3">
                    <div class="flex items-center">
                        <div class="bg-blue-50 p-2 rounded-lg mr-3">
                            <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800 text-sm truncate" title="${customerData.customerName}">${customerData.customerName}</h4>
                            <p class="text-xs text-gray-500">Customer</p>
                        </div>
                    </div>
                </div>

                <div class="space-y-2 mb-3">
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-gray-600">Debit Notes:</span>
                        <span class="text-sm font-medium text-indigo-600">${formatCurrency(customerData.totalDebit)}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-gray-600">Credit Notes:</span>
                        <span class="text-sm font-medium text-emerald-600">${formatCurrency(customerData.totalCredit)}</span>
                    </div>
                    <div class="border-t pt-2">
                        <div class="flex justify-between items-center">
                            <span class="text-xs font-medium text-gray-700">${netLabel}:</span>
                            <span class="text-sm font-bold ${netColor}">${formatCurrency(Math.abs(netAmount))}</span>
                        </div>
                    </div>
                </div>

                <div class="text-xs text-gray-500 flex justify-between">
                    <span>${customerData.debitNotes.length + customerData.creditNotes.length} transactions</span>
                    <span class="flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                        View Details
                    </span>
                </div>
            `;

            // Add click event to show customer details
            card.addEventListener('click', () => {
                showCustomerDetailsModal(customerId, customerData);
            });

            return card;
        }

        /**
         * Shows customer details modal
         * @param {string} customerId - Customer ID
         * @param {object} customerData - Customer data
         */
        function showCustomerDetailsModal(customerId, customerData) {
            // Create modal overlay
            const modalOverlay = document.createElement('div');
            modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';

            const netAmount = customerData.totalDebit - customerData.totalCredit;
            const netColor = netAmount >= 0 ? 'text-green-600' : 'text-red-600';
            const netLabel = netAmount >= 0 ? 'Net Receivable' : 'Net Payable';

            modalOverlay.innerHTML = `
                <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
                    <div class="flex items-center justify-between p-6 border-b border-gray-200">
                        <h3 class="text-xl font-semibold text-gray-800">Party Ledger - ${customerData.customerName}</h3>
                        <button id="close-customer-modal" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                        <!-- Summary -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="bg-indigo-50 p-4 rounded-lg">
                                <h4 class="text-sm font-medium text-indigo-800">Total Debit</h4>
                                <p class="text-2xl font-bold text-indigo-600">${formatCurrency(customerData.totalDebit)}</p>
                            </div>
                            <div class="bg-emerald-50 p-4 rounded-lg">
                                <h4 class="text-sm font-medium text-emerald-800">Total Credit</h4>
                                <p class="text-2xl font-bold text-emerald-600">${formatCurrency(customerData.totalCredit)}</p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="text-sm font-medium text-gray-800">${netLabel}</h4>
                                <p class="text-2xl font-bold ${netColor}">${formatCurrency(Math.abs(netAmount))}</p>
                            </div>
                        </div>

                        <!-- Transactions -->
                        <div class="space-y-6">
                            ${customerData.debitNotes.length > 0 ? `
                                <div>
                                    <h4 class="text-lg font-semibold text-indigo-600 mb-3">Debit Notes (${customerData.debitNotes.length})</h4>
                                    <div class="space-y-2">
                                        ${customerData.debitNotes.map(note => `
                                            <div class="bg-indigo-50 p-3 rounded border-l-4 border-indigo-500">
                                                <div class="flex justify-between items-start">
                                                    <div>
                                                        <p class="font-medium text-gray-800">${note.description}</p>
                                                        <p class="text-sm text-gray-600">Note: ${note.noteId} | Transaction: ${note.transactionId}</p>
                                                        <p class="text-xs text-gray-500">${new Date(note.timestamp).toLocaleString()}</p>
                                                    </div>
                                                    <span class="font-bold text-indigo-600">${formatCurrency(note.amount)}</span>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}

                            ${customerData.creditNotes.length > 0 ? `
                                <div>
                                    <h4 class="text-lg font-semibold text-emerald-600 mb-3">Credit Notes (${customerData.creditNotes.length})</h4>
                                    <div class="space-y-2">
                                        ${customerData.creditNotes.map(note => `
                                            <div class="bg-emerald-50 p-3 rounded border-l-4 border-emerald-500">
                                                <div class="flex justify-between items-start">
                                                    <div>
                                                        <p class="font-medium text-gray-800">${note.description}</p>
                                                        <p class="text-sm text-gray-600">Note: ${note.noteId} | Transaction: ${note.transactionId}</p>
                                                        <p class="text-xs text-gray-500">${new Date(note.timestamp).toLocaleString()}</p>
                                                    </div>
                                                    <span class="font-bold text-emerald-600">${formatCurrency(note.amount)}</span>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modalOverlay);

            // Add event listeners
            const closeModal = () => {
                document.body.removeChild(modalOverlay);
            };

            modalOverlay.querySelector('#close-customer-modal').addEventListener('click', closeModal);
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) closeModal();
            });
        }

        /**
         * Updates the time-based summary cards with counts
         * @param {object} debitNotes - Debit notes data
         * @param {object} creditNotes - Credit notes data
         * @param {string} timestamp - Creation timestamp from saved data
         */
        function updateSummary(debitNotes, creditNotes, timestamp) {
            const now = new Date();
            const startOfToday = getStartOfToday();
            const startOfWeek = getStartOfWeek();
            const startOfMonth = getStartOfMonth();
            const startOfYear = getStartOfYear();

            // Initialize counters
            let todayDebit = 0, todayCredit = 0;
            let weekDebit = 0, weekCredit = 0;
            let monthDebit = 0, monthCredit = 0;
            let yearDebit = 0, yearCredit = 0;
            let totalDebit = 0, totalCredit = 0;
            let totalDebitAmount = 0, totalCreditAmount = 0;

            // Count debit notes by time periods
            Object.keys(debitNotes).forEach(noteId => {
                totalDebit++;
                const noteData = debitNotes[noteId];
                totalDebitAmount += noteData.totalAmount || 0;

                // For time-based counting, we'll use the current timestamp as creation time
                // In a real application, each note would have its own creation timestamp
                const noteTimestamp = timestamp || new Date().toISOString();

                if (isWithinDateRange(noteTimestamp, startOfToday)) {
                    todayDebit++;
                }
                if (isWithinDateRange(noteTimestamp, startOfWeek)) {
                    weekDebit++;
                }
                if (isWithinDateRange(noteTimestamp, startOfMonth)) {
                    monthDebit++;
                }
                if (isWithinDateRange(noteTimestamp, startOfYear)) {
                    yearDebit++;
                }
            });

            // Count credit notes by time periods
            Object.keys(creditNotes).forEach(noteId => {
                totalCredit++;
                const noteData = creditNotes[noteId];
                totalCreditAmount += noteData.totalAmount || 0;

                // For time-based counting, we'll use the current timestamp as creation time
                const noteTimestamp = timestamp || new Date().toISOString();

                if (isWithinDateRange(noteTimestamp, startOfToday)) {
                    todayCredit++;
                }
                if (isWithinDateRange(noteTimestamp, startOfWeek)) {
                    weekCredit++;
                }
                if (isWithinDateRange(noteTimestamp, startOfMonth)) {
                    monthCredit++;
                }
                if (isWithinDateRange(noteTimestamp, startOfYear)) {
                    yearCredit++;
                }
            });

            // Calculate net amount (Money Received - Money Paid)
            const netAmount = totalDebitAmount - totalCreditAmount;

            // Update the original summary cards with amounts
            document.getElementById('total-debit-amount').textContent = formatCurrency(totalDebitAmount);
            document.getElementById('total-credit-amount').textContent = formatCurrency(totalCreditAmount);
            document.getElementById('net-amount').textContent = formatCurrency(netAmount);

            // Update the time-based summary cards
            document.getElementById('today-debit-count').textContent = todayDebit;
            document.getElementById('today-credit-count').textContent = todayCredit;
            document.getElementById('week-debit-count').textContent = weekDebit;
            document.getElementById('week-credit-count').textContent = weekCredit;
            document.getElementById('month-debit-count').textContent = monthDebit;
            document.getElementById('month-credit-count').textContent = monthCredit;
            document.getElementById('year-debit-count').textContent = yearDebit;
            document.getElementById('year-credit-count').textContent = yearCredit;
            document.getElementById('total-debit-count').textContent = totalDebit;
            document.getElementById('total-credit-count').textContent = totalCredit;
        }

        // Load and display notes when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            loadAndDisplayNotes();

            // Party Ledger button event listener
            const partyLedgerBtn = document.getElementById('party-ledger-btn');
            if (partyLedgerBtn) {
                partyLedgerBtn.addEventListener('click', () => {
                    loadAndDisplayPartyLedger();
                    // Scroll to party ledger section
                    document.getElementById('party-ledger-container').scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            }

            // Refresh data every 5 seconds in case it's updated in another tab
            setInterval(loadAndDisplayNotes, 5000);
        });
    </script>

</body>
</html>
